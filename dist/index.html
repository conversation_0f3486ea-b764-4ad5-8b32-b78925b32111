<!doctype html><html lang="en"><head><meta charset="UTF-8"><meta name="viewport" content="width=device-width,initial-scale=1"><title>Evidence Dashboard</title><style>/* Evidence Dashboard Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
}

.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 2rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.header h1 {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.header nav ul {
    list-style: none;
    display: flex;
    gap: 2rem;
}

.header nav a {
    color: white;
    text-decoration: none;
    font-weight: 500;
    transition: opacity 0.3s ease;
}

.header nav a:hover {
    opacity: 0.8;
}

.main-content {
    max-width: 1200px;
    margin: 2rem auto;
    padding: 0 2rem;
}

.section {
    background: white;
    border-radius: 8px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.section h2 {
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
    color: #2c3e50;
}

.card-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.card {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1.5rem;
    text-align: center;
    border-left: 4px solid #667eea;
}

.card h3 {
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.metric {
    font-size: 2.5rem;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.change {
    font-size: 0.9rem;
    font-weight: 600;
}

.change.positive {
    color: #28a745;
}

.change.negative {
    color: #dc3545;
}

.chart-container {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 3rem;
    text-align: center;
    border: 2px dashed #dee2e6;
}

.chart-placeholder {
    color: #6c757d;
    font-style: italic;
}

.table-container {
    overflow-x: auto;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
}

.data-table th,
.data-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #dee2e6;
}

.data-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #495057;
}

.data-table tbody tr:hover {
    background-color: #f8f9fa;
}

.footer {
    background: #2c3e50;
    color: white;
    text-align: center;
    padding: 2rem;
    margin-top: 3rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header {
        padding: 1rem;
    }
    
    .header nav ul {
        flex-direction: column;
        gap: 1rem;
    }
    
    .main-content {
        padding: 0 1rem;
    }
    
    .section {
        padding: 1.5rem;
    }
    
    .card-grid {
        grid-template-columns: 1fr;
    }
    
    .metric {
        font-size: 2rem;
    }
}

/* Domo DDX specific optimizations */
@media print {
    .header nav,
    .footer {
        display: none;
    }
    
    .section {
        box-shadow: none;
        border: 1px solid #dee2e6;
    }
}
</style><style>/* Evidence Dashboard Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
}

.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 2rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.header h1 {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.header nav ul {
    list-style: none;
    display: flex;
    gap: 2rem;
}

.header nav a {
    color: white;
    text-decoration: none;
    font-weight: 500;
    transition: opacity 0.3s ease;
}

.header nav a:hover {
    opacity: 0.8;
}

.main-content {
    max-width: 1200px;
    margin: 2rem auto;
    padding: 0 2rem;
}

.section {
    background: white;
    border-radius: 8px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.section h2 {
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
    color: #2c3e50;
}

.card-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.card {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1.5rem;
    text-align: center;
    border-left: 4px solid #667eea;
}

.card h3 {
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.metric {
    font-size: 2.5rem;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.change {
    font-size: 0.9rem;
    font-weight: 600;
}

.change.positive {
    color: #28a745;
}

.change.negative {
    color: #dc3545;
}

.chart-container {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 3rem;
    text-align: center;
    border: 2px dashed #dee2e6;
}

.chart-placeholder {
    color: #6c757d;
    font-style: italic;
}

.table-container {
    overflow-x: auto;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
}

.data-table th,
.data-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #dee2e6;
}

.data-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #495057;
}

.data-table tbody tr:hover {
    background-color: #f8f9fa;
}

.footer {
    background: #2c3e50;
    color: white;
    text-align: center;
    padding: 2rem;
    margin-top: 3rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header {
        padding: 1rem;
    }
    
    .header nav ul {
        flex-direction: column;
        gap: 1rem;
    }
    
    .main-content {
        padding: 0 1rem;
    }
    
    .section {
        padding: 1.5rem;
    }
    
    .card-grid {
        grid-template-columns: 1fr;
    }
    
    .metric {
        font-size: 2rem;
    }
}

/* Domo DDX specific optimizations */
@media print {
    .header nav,
    .footer {
        display: none;
    }
    
    .section {
        box-shadow: none;
        border: 1px solid #dee2e6;
    }
}
</style></head><body><div id="app"><header class="header"><h1>Evidence Dashboard</h1><nav><ul><li><a href="#overview">Overview</a></li><li><a href="#analytics">Analytics</a></li><li><a href="#reports">Reports</a></li></ul></nav></header><main class="main-content"><section id="overview" class="section"><h2>Overview</h2><div class="card-grid"><div class="card"><h3>Total Users</h3><div class="metric">1,234</div><div class="change positive">+12%</div></div><div class="card"><h3>Revenue</h3><div class="metric">$45,678</div><div class="change positive">+8%</div></div><div class="card"><h3>Conversion Rate</h3><div class="metric">3.2%</div><div class="change negative">-2%</div></div></div></section><section id="analytics" class="section"><h2>Analytics</h2><div class="chart-container"><div class="chart-placeholder"><p>Chart would be rendered here by Evidence</p></div></div></section><section id="reports" class="section"><h2>Reports</h2><div class="table-container"><table class="data-table"><thead><tr><th>Date</th><th>Metric</th><th>Value</th><th>Change</th></tr></thead><tbody><tr><td>2024-01-01</td><td>Users</td><td>1,000</td><td class="positive">+5%</td></tr><tr><td>2024-01-02</td><td>Revenue</td><td>$42,000</td><td class="positive">+3%</td></tr><tr><td>2024-01-03</td><td>Conversion</td><td>3.5%</td><td class="negative">-1%</td></tr></tbody></table></div></section></main><footer class="footer"><p>&copy; 2024 Evidence Dashboard. Built for Domo DDX.</p></footer></div><script>// Evidence Dashboard JavaScript
document.addEventListener('DOMContentLoaded', function() {
    console.log('Evidence Dashboard loaded');
    
    // Smooth scrolling for navigation links
    const navLinks = document.querySelectorAll('.header nav a');
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);
            
            if (targetElement) {
                targetElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
    
    // Add some interactivity to cards
    const cards = document.querySelectorAll('.card');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
            this.style.transition = 'transform 0.3s ease';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
    
    // Simulate real-time data updates (for demo purposes)
    function updateMetrics() {
        const metrics = document.querySelectorAll('.metric');
        metrics.forEach(metric => {
            const currentValue = metric.textContent;
            // Add a subtle animation to indicate data refresh
            metric.style.opacity = '0.7';
            setTimeout(() => {
                metric.style.opacity = '1';
            }, 200);
        });
    }
    
    // Update metrics every 30 seconds (for demo)
    setInterval(updateMetrics, 30000);
    
    // Add loading state management for Domo DDX
    window.domoReady = function() {
        console.log('Domo DDX environment ready');
        document.body.classList.add('domo-ready');
    };
    
    // Check if running in Domo environment
    if (window.domo) {
        console.log('Running in Domo DDX environment');
        window.domoReady();
    } else {
        console.log('Running in standalone mode');
        // Simulate Domo environment for testing
        setTimeout(() => {
            window.domoReady();
        }, 1000);
    }
    
    // Error handling for production
    window.addEventListener('error', function(e) {
        console.error('Dashboard error:', e.error);
        // In a real app, you might want to report this to an error tracking service
    });
    
    // Performance monitoring
    if ('performance' in window) {
        window.addEventListener('load', function() {
            setTimeout(() => {
                const perfData = performance.getEntriesByType('navigation')[0];
                console.log('Page load time:', perfData.loadEventEnd - perfData.loadEventStart, 'ms');
            }, 0);
        });
    }
});

// Export for potential use in Domo DDX
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        init: function() {
            console.log('Evidence Dashboard module initialized');
        }
    };
}
</script><script src="bundle.js"></script></body></html>
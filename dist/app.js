document.addEventListener("DOMContentLoaded",(function(){console.log("Evidence Dashboard loaded"),document.querySelectorAll(".header nav a").forEach((e=>{e.addEventListener("click",(function(e){e.preventDefault();const o=this.getAttribute("href").substring(1),n=document.getElementById(o);n&&n.scrollIntoView({behavior:"smooth",block:"start"})}))})),document.querySelectorAll(".card").forEach((e=>{e.addEventListener("mouseenter",(function(){this.style.transform="translateY(-2px)",this.style.transition="transform 0.3s ease"})),e.addEventListener("mouseleave",(function(){this.style.transform="translateY(0)"}))})),setInterval((function(){document.querySelectorAll(".metric").forEach((e=>{e.textContent,e.style.opacity="0.7",setTimeout((()=>{e.style.opacity="1"}),200)}))}),3e4),window.domoReady=function(){console.log("Domo DDX environment ready"),document.body.classList.add("domo-ready")},window.domo?(console.log("Running in Domo DDX environment"),window.domoReady()):(console.log("Running in standalone mode"),setTimeout((()=>{window.domoReady()}),1e3)),window.addEventListener("error",(function(e){console.error("Dashboard error:",e.error)})),"performance"in window&&window.addEventListener("load",(function(){setTimeout((()=>{const e=performance.getEntriesByType("navigation")[0];console.log("Page load time:",e.loadEventEnd-e.loadEventStart,"ms")}),0)}))})),"undefined"!=typeof module&&module.exports&&(module.exports={init:function(){console.log("Evidence Dashboard module initialized")}});
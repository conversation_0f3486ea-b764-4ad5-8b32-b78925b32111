/* Evidence Dashboard Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
}

.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 2rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.header h1 {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.header nav ul {
    list-style: none;
    display: flex;
    gap: 2rem;
}

.header nav a {
    color: white;
    text-decoration: none;
    font-weight: 500;
    transition: opacity 0.3s ease;
}

.header nav a:hover {
    opacity: 0.8;
}

.main-content {
    max-width: 1200px;
    margin: 2rem auto;
    padding: 0 2rem;
}

.section {
    background: white;
    border-radius: 8px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.section h2 {
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
    color: #2c3e50;
}

.card-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.card {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1.5rem;
    text-align: center;
    border-left: 4px solid #667eea;
}

.card h3 {
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.metric {
    font-size: 2.5rem;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.change {
    font-size: 0.9rem;
    font-weight: 600;
}

.change.positive {
    color: #28a745;
}

.change.negative {
    color: #dc3545;
}

.chart-container {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 3rem;
    text-align: center;
    border: 2px dashed #dee2e6;
}

.chart-placeholder {
    color: #6c757d;
    font-style: italic;
}

.table-container {
    overflow-x: auto;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
}

.data-table th,
.data-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #dee2e6;
}

.data-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #495057;
}

.data-table tbody tr:hover {
    background-color: #f8f9fa;
}

.footer {
    background: #2c3e50;
    color: white;
    text-align: center;
    padding: 2rem;
    margin-top: 3rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header {
        padding: 1rem;
    }
    
    .header nav ul {
        flex-direction: column;
        gap: 1rem;
    }
    
    .main-content {
        padding: 0 1rem;
    }
    
    .section {
        padding: 1.5rem;
    }
    
    .card-grid {
        grid-template-columns: 1fr;
    }
    
    .metric {
        font-size: 2rem;
    }
}

/* Domo DDX specific optimizations */
@media print {
    .header nav,
    .footer {
        display: none;
    }
    
    .section {
        box-shadow: none;
        border: 1px solid #dee2e6;
    }
}


{"name": "@webassemblyjs/ieee754", "version": "1.13.2", "description": "IEEE754 decoder and encoder", "license": "MIT", "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "https://github.com/xtuc/webassemblyjs.git", "directory": "packages/ieee754"}, "publishConfig": {"access": "public"}, "dependencies": {"@xtuc/ieee754": "^1.2.0"}, "gitHead": "897aeb784f042a46a00626f1d1cca96159aa5db3"}
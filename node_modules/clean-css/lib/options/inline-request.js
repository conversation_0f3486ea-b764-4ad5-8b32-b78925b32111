var url = require('url');

var override = require('../utils/override');

function inlineRequestFrom(option) {
  return override(
    /* jshint camelcase: false */
    proxyOptionsFrom(process.env.HTTP_PROXY || process.env.http_proxy),
    option || {}
  );
}

function proxyOptionsFrom(httpProxy) {
  return httpProxy
    ? {
      hostname: url.parse(httpProxy).hostname,
      port: parseInt(url.parse(httpProxy).port)
    }
    : {};
}

module.exports = inlineRequestFrom;

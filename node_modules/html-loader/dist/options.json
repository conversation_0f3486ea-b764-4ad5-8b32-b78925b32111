{"title": "HTML Loader options", "type": "object", "definitions": {"Source": {"anyOf": [{"type": "object", "properties": {"tag": {"type": "string", "minLength": 1}, "attribute": {"type": "string", "minLength": 1}, "type": {"enum": ["src", "srcset"]}, "filter": {"instanceof": "Function"}}, "required": ["attribute", "type"], "additionalProperties": false}, {"enum": ["..."]}]}, "SourcesList": {"type": "array", "items": {"$ref": "#/definitions/Source"}, "minItems": 1, "uniqueItems": true}}, "properties": {"preprocessor": {"instanceof": "Function", "description": "Allows pre-processing of content before handling.", "link": "https://github.com/webpack-contrib/html-loader#preprocessor"}, "sources": {"anyOf": [{"type": "boolean"}, {"type": "object", "properties": {"list": {"$ref": "#/definitions/SourcesList"}, "urlFilter": {"instanceof": "Function"}, "scriptingEnabled": {"type": "boolean"}}, "additionalProperties": false}], "description": "By default every loadable attributes (for example - <img src='image.png'>) is imported (const img = require('./image.png'). You may need to specify loaders for images in your configuration.", "link": "https://github.com/webpack-contrib/html-loader#sources"}, "minimize": {"anyOf": [{"type": "boolean"}, {"type": "object"}], "description": "Tell html-loader to minimize HTML.", "link": "https://github.com/webpack-contrib/html-loader#minimize"}, "esModule": {"type": "boolean", "description": "Enable or disable ES modules syntax.", "link": "https://github.com/webpack-contrib/html-loader#esmodule"}}, "additionalProperties": false}
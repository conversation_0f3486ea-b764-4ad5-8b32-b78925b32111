{"name": "html-loader", "version": "4.2.0", "description": "Html loader module for webpack", "license": "MIT", "repository": "webpack-contrib/html-loader", "author": "<PERSON> @sokra", "homepage": "https://github.com/webpack-contrib/html-loader", "bugs": "https://github.com/webpack-contrib/html-loader/issues", "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "main": "dist/cjs.js", "engines": {"node": ">= 14.15.0"}, "scripts": {"start": "npm run build -- -w", "clean": "del-cli dist", "validate:runtime": "es-check es5 \"dist/runtime/**/*.js\"", "prebuild": "npm run clean", "build": "cross-env NODE_ENV=production babel src -d dist --copy-files", "postbuild": "npm run validate:runtime", "commitlint": "commitlint --from=master", "security": "npm audit --production", "lint:prettier": "prettier --list-different .", "lint:js": "eslint --cache .", "lint": "npm-run-all -l -p \"lint:**\"", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "npm run test:only -- --watch", "test:coverage": "npm run test:only -- --coverage", "pretest": "npm run lint", "test": "npm run test:coverage", "prepare": "husky install && npm run build", "release": "standard-version"}, "files": ["dist"], "peerDependencies": {"webpack": "^5.0.0"}, "dependencies": {"html-minifier-terser": "^7.0.0", "parse5": "^7.0.0"}, "devDependencies": {"@babel/cli": "^7.18.10", "@babel/core": "^7.18.13", "@babel/preset-env": "^7.18.10", "@commitlint/cli": "^17.1.2", "@commitlint/config-conventional": "^17.1.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^29.0.3", "cross-env": "^7.0.3", "del": "^6.1.1", "del-cli": "^4.0.0", "es-check": "^7.0.0", "eslint": "^8.23.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-import": "^2.26.0", "handlebars": "^4.7.7", "html-webpack-plugin": "^5.3.2", "husky": "^8.0.1", "jest": "^29.0.3", "lint-staged": "^13.0.3", "memfs": "^3.4.7", "npm-run-all": "^4.1.5", "posthtml": "^0.16.6", "posthtml-webp": "^2.2.0", "prettier": "^2.7.1", "standard-version": "^9.5.0", "unescape-unicode": "^0.2.0", "webpack": "^5.74.0"}, "keywords": ["webpack", "html", "loader"]}
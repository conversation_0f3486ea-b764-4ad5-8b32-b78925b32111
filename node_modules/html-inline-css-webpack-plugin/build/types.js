(function (factory) {
    if (typeof module === "object" && typeof module.exports === "object") {
        var v = factory(require, exports);
        if (v !== undefined) module.exports = v;
    }
    else if (typeof define === "function" && define.amd) {
        define(["require", "exports"], factory);
    }
})(function (require, exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.DEFAULT_REPLACE_CONFIG = exports.TAP_KEY_PREFIX = void 0;
    exports.TAP_KEY_PREFIX = 'html-inline-css-webpack-plugin';
    exports.DEFAULT_REPLACE_CONFIG = {
        target: '</head>',
    };
});
//# sourceMappingURL=types.js.map
(function (factory) {
    if (typeof module === "object" && typeof module.exports === "object") {
        var v = factory(require, exports);
        if (v !== undefined) module.exports = v;
    }
    else if (typeof define === "function" && define.amd) {
        define(["require", "exports", "html-webpack-plugin", "./core"], factory);
    }
})(function (require, exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    var HTMLWebpackPlugin = require("html-webpack-plugin");
    var core_1 = require("./core");
    var isHTMLWebpackPluginV4 = 'getHooks' in HTMLWebpackPlugin;
    exports.default = isHTMLWebpackPluginV4
        ? core_1.PluginForHtmlWebpackPluginV4
        : core_1.PluginForHtmlWebpackPluginV3;
});
//# sourceMappingURL=index.js.map
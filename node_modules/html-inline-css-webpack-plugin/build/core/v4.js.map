{"version": 3, "file": "v4.js", "sourceRoot": "", "sources": ["../../src/core/v4.ts"], "names": [], "mappings": ";;;;;;;;;;;;;IAEA,uDAAyD;IAEzD,kCAAyC;IACzC,6CAA0C;IAwB1C;QAAkD,wDAAU;QAA5D;YAAA,qEAyEC;YAxEC,kEAAkE;YAC1D,iBAAW,GAAuC,IAAI,GAAG,EAAE,CAAA;;QAuErE,CAAC;QArES,sDAAe,GAAvB,UAAwB,IAAkC;YAA1D,iBA4BC;YA3BC,wEAAwE;YACxE,qEAAqE;YACrE,qEAAqE;YACrE,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAA;YAElB,IAAA,KAAA,eAAiB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAA,EAA5B,SAAS,cAAmB,CAAA;YACtC,SAAS,CAAC,OAAO,CAAC,UAAC,OAAO;gBACxB,IAAI,KAAI,CAAC,6BAA6B,CAAC,OAAO,CAAC,EAAE;oBAC/C,IAAM,KAAK,GAAG,KAAI,CAAC,WAAW,CAAC;wBAC7B,OAAO,SAAA;wBACP,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,UAAU;qBACnC,CAAC,CAAA;oBAEF,IAAI,KAAK,EAAE;wBACT,IAAI,KAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;4BACrC,KAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;yBAC/C;6BAAM;4BACL,KAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,CAAC,CAAA;yBAC3C;wBACD,IAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;wBACrD,gCAAgC;wBAChC,IAAI,YAAY,KAAK,CAAC,CAAC,EAAE;4BACvB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC,CAAC,CAAA;yBACxC;qBACF;iBACF;YACH,CAAC,CAAC,CAAA;QACJ,CAAC;QAEO,8CAAO,GAAf,UAAgB,IAAoB;YAApC,iBAeC;YAdC,4CAA4C;YAC5C,IAAI,IAAI,CAAC,6BAA6B,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;gBACvD,IAAM,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAA;gBAEzD,SAAS,CAAC,OAAO,CAAC,UAAC,KAAK;oBACtB,IAAI,CAAC,IAAI,GAAG,KAAI,CAAC,QAAQ,CAAC;wBACxB,KAAK,OAAA;wBACL,IAAI,EAAE,IAAI,CAAC,IAAI;wBACf,YAAY,EAAE,IAAI,CAAC,UAAU;qBAC9B,CAAC,CAAA;gBACJ,CAAC,CAAC,CAAA;gBAEF,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;aACpC;QACH,CAAC;QAED,4CAAK,GAAL,UAAM,QAAkB;YAAxB,iBAqBC;YApBC,QAAQ,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,CAC5B,UAAG,sBAAc,iBAAc,EAC/B,UAAC,WAAW;gBACV,IAAM,KAAK,GAA4B,iBAAyB,CAAC,QAAQ,CACvE,WAAW,CACZ,CAAA;gBAED,KAAK,CAAC,wBAAwB,CAAC,GAAG,CAChC,UAAG,sBAAc,8BAA2B,EAC5C,UAAC,IAAI;oBACH,KAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAA;oBACzB,KAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAA;gBAC5B,CAAC,CACF,CAAA;gBAED,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,UAAG,sBAAc,gBAAa,EAAE,UAAC,IAAI;oBACxD,KAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;gBACpB,CAAC,CAAC,CAAA;YACJ,CAAC,CACF,CAAA;QACH,CAAC;QACH,mCAAC;IAAD,CAAC,AAzED,CAAkD,wBAAU,GAyE3D;IAzEY,oEAA4B"}
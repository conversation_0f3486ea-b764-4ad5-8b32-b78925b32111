(function (factory) {
    if (typeof module === "object" && typeof module.exports === "object") {
        var v = factory(require, exports);
        if (v !== undefined) module.exports = v;
    }
    else if (typeof define === "function" && define.amd) {
        define(["require", "exports", "tslib", "../types", "./base-plugin"], factory);
    }
})(function (require, exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.PluginForHtmlWebpackPluginV3 = void 0;
    var tslib_1 = require("tslib");
    var types_1 = require("../types");
    var base_plugin_1 = require("./base-plugin");
    var PluginForHtmlWebpackPluginV3 = /** @class */ (function (_super) {
        tslib_1.__extends(PluginForHtmlWebpackPluginV3, _super);
        function PluginForHtmlWebpackPluginV3() {
            return _super !== null && _super.apply(this, arguments) || this;
        }
        PluginForHtmlWebpackPluginV3.prototype.process = function (data) {
            var _this = this;
            // check if current html needs to be inlined
            if (this.isCurrentFileNeedsToBeInlined(data.outputName)) {
                var _a = tslib_1.__read(data.assets.css), cssAssets = _a.slice(0);
                cssAssets.forEach(function (cssLink) {
                    var style = _this.getCSSStyle({
                        cssLink: cssLink,
                        publicPath: data.assets.publicPath,
                    });
                    if (style) {
                        data.html = _this.addStyle({
                            html: data.html,
                            htmlFileName: data.outputName,
                            style: style,
                        });
                        var cssLinkIndex = data.assets.css.indexOf(cssLink);
                        // prevent generate <link /> tag
                        if (cssLinkIndex !== -1) {
                            data.assets.css.splice(cssLinkIndex, 1);
                        }
                    }
                });
                data.html = this.cleanUp(data.html);
            }
        };
        PluginForHtmlWebpackPluginV3.prototype.apply = function (compiler) {
            var _this = this;
            compiler.hooks.compilation.tap("".concat(types_1.TAP_KEY_PREFIX, "_compilation"), function (compilation) {
                if ('htmlWebpackPluginBeforeHtmlProcessing' in compilation.hooks) {
                    var hook = compilation.hooks.htmlWebpackPluginBeforeHtmlProcessing;
                    hook.tap("".concat(types_1.TAP_KEY_PREFIX, "_htmlWebpackPluginBeforeHtmlProcessing"), function (data) {
                        _this.prepare(compilation);
                        _this.process(data);
                    });
                }
                else {
                    throw new Error('`html-webpack-plugin` should be ordered first before html-inline-css-webpack-plugin');
                }
            });
        };
        return PluginForHtmlWebpackPluginV3;
    }(base_plugin_1.BasePlugin));
    exports.PluginForHtmlWebpackPluginV3 = PluginForHtmlWebpackPluginV3;
});
//# sourceMappingURL=v3.js.map
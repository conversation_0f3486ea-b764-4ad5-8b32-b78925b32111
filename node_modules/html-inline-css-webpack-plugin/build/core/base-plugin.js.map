{"version": 3, "file": "base-plugin.js", "sourceRoot": "", "sources": ["../../src/core/base-plugin.ts"], "names": [], "mappings": ";;;;;;;;;;;;IAEA,kCAKiB;IACjB,kCAA8C;IAE9C;QAcE,oBAA+B,MAAmB;YAAnB,uBAAA,EAAA,WAAmB;YAAnB,WAAM,GAAN,MAAM,CAAa;YAbxC,kBAAa,GAAc,EAAE,CAAA;QAac,CAAC;QAXtD,sBAAc,qCAAa;iBAA3B;gBACE,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,IAAI,8BAAsB,CAAA;YACtD,CAAC;;;WAAA;QAED,sBAAc,uCAAe;iBAA7B;gBACE,OAAO,CACL,IAAI,CAAC,MAAM,CAAC,eAAe;oBAC3B,CAAC,UAAC,EAAS;4BAAP,KAAK,WAAA;wBAAO,OAAA,mCAA0B,KAAK,aAAU;oBAAzC,CAAyC,CAAC,CAC3D,CAAA;YACH,CAAC;;;WAAA;QAIS,4BAAO,GAAjB,UAAkB,EAAuB;YAAzC,iBAWC;gBAXmB,MAAM,YAAA;YACxB,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,UAAC,QAAQ;gBACnC,IAAI,IAAA,aAAK,EAAC,QAAQ,CAAC,IAAI,KAAI,CAAC,6BAA6B,CAAC,QAAQ,CAAC,EAAE;oBACnE,IAAM,MAAM,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC,MAAM,EAAE,CAAA;oBACxC,KAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,GAAG,OAAO,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAA;oBAEtF,IAAI,CAAC,KAAI,CAAC,MAAM,CAAC,YAAY,EAAE;wBAC7B,OAAO,MAAM,CAAC,QAAQ,CAAC,CAAA;qBACxB;iBACF;YACH,CAAC,CAAC,CAAA;QACJ,CAAC;QAES,gCAAW,GAArB,UAAsB,EAMrB;gBALC,OAAO,aAAA,EACP,UAAU,gBAAA;YAKV,mDAAmD;YACnD,IAAM,QAAQ,GAAG,OAAO;iBACrB,OAAO,CAAC,IAAI,MAAM,CAAC,WAAI,IAAA,oBAAY,EAAC,UAAU,CAAC,CAAE,CAAC,EAAE,EAAE,CAAC;iBACvD,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAA;YAExB,IAAI,IAAI,CAAC,6BAA6B,CAAC,QAAQ,CAAC,EAAE;gBAChD,IAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAA;gBAE1C,IAAI,KAAK,KAAK,SAAS,EAAE;oBACvB,OAAO,CAAC,KAAK,CACX,oCAA6B,OAAO,yDAAsD,CAC3F,CAAA;iBACF;gBAED,OAAO,KAAK,CAAA;aACb;iBAAM;gBACL,OAAO,SAAS,CAAA;aACjB;QACH,CAAC;QAES,kDAA6B,GAAvC,UAAwC,QAAgB;YACtD,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,UAAU,EAAE;gBAC5C,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAA;aACpC;iBAAM;gBACL,OAAO,IAAI,CAAA;aACZ;QACH,CAAC;QAES,6BAAQ,GAAlB,UAAmB,EAQlB;gBAPC,IAAI,UAAA,EACJ,YAAY,kBAAA,EACZ,KAAK,WAAA;YAML,IAAM,aAAa,GAAG;gBACpB,IAAI,CAAC,eAAe,CAAC,EAAE,KAAK,OAAA,EAAE,CAAC;gBAC/B,IAAI,CAAC,aAAa,CAAC,MAAM;aAC1B,CAAA;YAED,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,KAAK,OAAO,EAAE;gBAC3C,aAAa,CAAC,OAAO,EAAE,CAAA;aACxB;YAED,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE;gBAClD,MAAM,IAAI,KAAK,CACb,0CAAkC,YAAY,kDAAsC,IAAI,CAAC,aAAa,CAAC,MAAM,OAAG,CACjH,CAAA;aACF;YAED,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAA;QACxE,CAAC;QAES,4BAAO,GAAjB,UAAkB,IAAY;YAC5B,OAAO,IAAI,CAAC,aAAa,CAAC,YAAY;gBACpC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC;gBAC7C,CAAC,CAAC,IAAI,CAAA;QACV,CAAC;QACH,iBAAC;IAAD,CAAC,AAhGD,IAgGC;IAhGY,gCAAU"}
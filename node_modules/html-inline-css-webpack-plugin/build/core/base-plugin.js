(function (factory) {
    if (typeof module === "object" && typeof module.exports === "object") {
        var v = factory(require, exports);
        if (v !== undefined) module.exports = v;
    }
    else if (typeof define === "function" && define.amd) {
        define(["require", "exports", "../types", "../utils"], factory);
    }
})(function (require, exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.BasePlugin = void 0;
    var types_1 = require("../types");
    var utils_1 = require("../utils");
    var BasePlugin = /** @class */ (function () {
        function BasePlugin(config) {
            if (config === void 0) { config = {}; }
            this.config = config;
            this.cssStyleCache = {};
        }
        Object.defineProperty(BasePlugin.prototype, "replaceConfig", {
            get: function () {
                return this.config.replace || types_1.DEFAULT_REPLACE_CONFIG;
            },
            enumerable: false,
            configurable: true
        });
        Object.defineProperty(BasePlugin.prototype, "styleTagFactory", {
            get: function () {
                return (this.config.styleTagFactory ||
                    (function (_a) {
                        var style = _a.style;
                        return "<style type=\"text/css\">".concat(style, "</style>");
                    }));
            },
            enumerable: false,
            configurable: true
        });
        BasePlugin.prototype.prepare = function (_a) {
            var _this = this;
            var assets = _a.assets;
            Object.keys(assets).forEach(function (fileName) {
                if ((0, utils_1.isCSS)(fileName) && _this.isCurrentFileNeedsToBeInlined(fileName)) {
                    var source = assets[fileName].source();
                    _this.cssStyleCache[fileName] = typeof source === 'string' ? source : source.toString();
                    if (!_this.config.leaveCSSFile) {
                        delete assets[fileName];
                    }
                }
            });
        };
        BasePlugin.prototype.getCSSStyle = function (_a) {
            var cssLink = _a.cssLink, publicPath = _a.publicPath;
            // Link pattern: publicPath + fileName + '?' + hash
            var fileName = cssLink
                .replace(new RegExp("^".concat((0, utils_1.escapeRegExp)(publicPath))), '')
                .replace(/\?.+$/g, '');
            if (this.isCurrentFileNeedsToBeInlined(fileName)) {
                var style = this.cssStyleCache[fileName];
                if (style === undefined) {
                    console.error("Can not get css style for ".concat(cssLink, ". It may be a bug of html-inline-css-webpack-plugin."));
                }
                return style;
            }
            else {
                return undefined;
            }
        };
        BasePlugin.prototype.isCurrentFileNeedsToBeInlined = function (fileName) {
            if (typeof this.config.filter === 'function') {
                return this.config.filter(fileName);
            }
            else {
                return true;
            }
        };
        BasePlugin.prototype.addStyle = function (_a) {
            var html = _a.html, htmlFileName = _a.htmlFileName, style = _a.style;
            var replaceValues = [
                this.styleTagFactory({ style: style }),
                this.replaceConfig.target,
            ];
            if (this.replaceConfig.position === 'after') {
                replaceValues.reverse();
            }
            if (html.indexOf(this.replaceConfig.target) === -1) {
                throw new Error("Can not inject css style into \"".concat(htmlFileName, "\", as there is not replace target \"").concat(this.replaceConfig.target, "\""));
            }
            return html.replace(this.replaceConfig.target, replaceValues.join(''));
        };
        BasePlugin.prototype.cleanUp = function (html) {
            return this.replaceConfig.removeTarget
                ? html.replace(this.replaceConfig.target, '')
                : html;
        };
        return BasePlugin;
    }());
    exports.BasePlugin = BasePlugin;
});
//# sourceMappingURL=base-plugin.js.map
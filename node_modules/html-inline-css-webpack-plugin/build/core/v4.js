(function (factory) {
    if (typeof module === "object" && typeof module.exports === "object") {
        var v = factory(require, exports);
        if (v !== undefined) module.exports = v;
    }
    else if (typeof define === "function" && define.amd) {
        define(["require", "exports", "tslib", "html-webpack-plugin", "../types", "./base-plugin"], factory);
    }
})(function (require, exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.PluginForHtmlWebpackPluginV4 = void 0;
    var tslib_1 = require("tslib");
    var HTMLWebpackPlugin = require("html-webpack-plugin");
    var types_1 = require("../types");
    var base_plugin_1 = require("./base-plugin");
    var PluginForHtmlWebpackPluginV4 = /** @class */ (function (_super) {
        tslib_1.__extends(PluginForHtmlWebpackPluginV4, _super);
        function PluginForHtmlWebpackPluginV4() {
            var _this = _super !== null && _super.apply(this, arguments) || this;
            // Using object reference to distinguish styles for multiple files
            _this.cssStyleMap = new Map();
            return _this;
        }
        PluginForHtmlWebpackPluginV4.prototype.prepareCSSStyle = function (data) {
            var _this = this;
            // `prepareCSSStyle` may be called more than once in webpack watch mode.
            // https://github.com/Runjuu/html-inline-css-webpack-plugin/issues/30
            // https://github.com/Runjuu/html-inline-css-webpack-plugin/issues/13
            this.cssStyleMap.clear();
            var _a = tslib_1.__read(data.assets.css), cssAssets = _a.slice(0);
            cssAssets.forEach(function (cssLink) {
                if (_this.isCurrentFileNeedsToBeInlined(cssLink)) {
                    var style = _this.getCSSStyle({
                        cssLink: cssLink,
                        publicPath: data.assets.publicPath,
                    });
                    if (style) {
                        if (_this.cssStyleMap.has(data.plugin)) {
                            _this.cssStyleMap.get(data.plugin).push(style);
                        }
                        else {
                            _this.cssStyleMap.set(data.plugin, [style]);
                        }
                        var cssLinkIndex = data.assets.css.indexOf(cssLink);
                        // prevent generate <link /> tag
                        if (cssLinkIndex !== -1) {
                            data.assets.css.splice(cssLinkIndex, 1);
                        }
                    }
                }
            });
        };
        PluginForHtmlWebpackPluginV4.prototype.process = function (data) {
            var _this = this;
            // check if current html needs to be inlined
            if (this.isCurrentFileNeedsToBeInlined(data.outputName)) {
                var cssStyles = this.cssStyleMap.get(data.plugin) || [];
                cssStyles.forEach(function (style) {
                    data.html = _this.addStyle({
                        style: style,
                        html: data.html,
                        htmlFileName: data.outputName,
                    });
                });
                data.html = this.cleanUp(data.html);
            }
        };
        PluginForHtmlWebpackPluginV4.prototype.apply = function (compiler) {
            var _this = this;
            compiler.hooks.compilation.tap("".concat(types_1.TAP_KEY_PREFIX, "_compilation"), function (compilation) {
                var hooks = HTMLWebpackPlugin.getHooks(compilation);
                hooks.beforeAssetTagGeneration.tap("".concat(types_1.TAP_KEY_PREFIX, "_beforeAssetTagGeneration"), function (data) {
                    _this.prepare(compilation);
                    _this.prepareCSSStyle(data);
                });
                hooks.beforeEmit.tap("".concat(types_1.TAP_KEY_PREFIX, "_beforeEmit"), function (data) {
                    _this.process(data);
                });
            });
        };
        return PluginForHtmlWebpackPluginV4;
    }(base_plugin_1.BasePlugin));
    exports.PluginForHtmlWebpackPluginV4 = PluginForHtmlWebpackPluginV4;
});
//# sourceMappingURL=v4.js.map
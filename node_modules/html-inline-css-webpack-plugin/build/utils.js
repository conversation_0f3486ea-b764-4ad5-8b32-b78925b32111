(function (factory) {
    if (typeof module === "object" && typeof module.exports === "object") {
        var v = factory(require, exports);
        if (v !== undefined) module.exports = v;
    }
    else if (typeof define === "function" && define.amd) {
        define(["require", "exports", "lodash"], factory);
    }
})(function (require, exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.isCSS = exports.is = exports.escapeRegExp = void 0;
    var lodash_1 = require("lodash");
    Object.defineProperty(exports, "escapeRegExp", { enumerable: true, get: function () { return lodash_1.escapeRegExp; } });
    function is(filenameExtension) {
        var reg = new RegExp(".".concat(filenameExtension, "$"));
        return function (fileName) { return reg.test(fileName); };
    }
    exports.is = is;
    exports.isCSS = is('css');
});
//# sourceMappingURL=utils.js.map
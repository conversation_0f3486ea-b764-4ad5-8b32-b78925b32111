const path = require('path');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const CopyPlugin = require('copy-webpack-plugin');
const fs = require('fs');

// ⬇️ Customize this if Evidence output is elsewhere
const EVIDENCE_BUILD_DIR = path.resolve(__dirname, 'evidence_build');
const EVIDENCE_INDEX = path.resolve(EVIDENCE_BUILD_DIR, 'index.html');



// Create a simple entry point that imports all CSS files from Evidence build
const createEntryPoint = () => {
  const entryPath = path.resolve(__dirname, 'temp-entry.js');
  let entryContent = '// Auto-generated entry point for Evidence build\n';

  // Check if Evidence build directory exists
  if (fs.existsSync(EVIDENCE_BUILD_DIR)) {
    // Find all CSS files in the Evidence build
    const findCssFiles = (dir) => {
      const files = [];
      if (fs.existsSync(dir)) {
        const items = fs.readdirSync(dir);
        items.forEach(item => {
          const fullPath = path.join(dir, item);
          const stat = fs.statSync(fullPath);
          if (stat.isDirectory()) {
            files.push(...findCssFiles(fullPath));
          } else if (item.endsWith('.css')) {
            files.push(fullPath);
          }
        });
      }
      return files;
    };

    const cssFiles = findCssFiles(EVIDENCE_BUILD_DIR);
    cssFiles.forEach(cssFile => {
      const relativePath = path.relative(__dirname, cssFile);
      entryContent += `import './${relativePath.replace(/\\/g, '/')}';\n`;
    });
  }

  // Write the entry file
  fs.writeFileSync(entryPath, entryContent);
  return entryPath;
};

module.exports = {
  mode: 'production',

  // Create dynamic entry point
  entry: createEntryPoint(),

  output: {
    path: path.resolve(__dirname, 'dist'),
    filename: 'temp-bundle.js', // Will be deleted by cleanup
    clean: true,
  },

  module: {
    rules: [
      {
        test: /\.html$/,
        loader: 'html-loader',
        options: {
          sources: {
            list: [
              {
                tag: 'img',
                attribute: 'src',
                type: 'src',
              },
              {
                tag: 'link',
                attribute: 'href',
                type: 'src',
                filter: (tag, attribute, attributes) => {
                  return attributes.rel && attributes.rel.includes('stylesheet');
                },
              },
            ],
          },
        },
      },
      {
        test: /\.css$/i,
        use: [MiniCssExtractPlugin.loader, 'css-loader'],
      },
      {
        test: /\.(png|jpe?g|gif|svg|ico|webp)$/i,
        type: 'asset/resource',
        generator: {
          filename: 'assets/[name][ext]',
        },
      },
      {
        test: /\.(woff|woff2|eot|ttf|otf)$/i,
        type: 'asset/resource',
        generator: {
          filename: 'assets/fonts/[name][ext]',
        },
      },
    ],
  },

  plugins: [
    new MiniCssExtractPlugin({
      filename: 'styles.css',
      chunkFilename: 'styles.css',
    }),
    new HtmlWebpackPlugin({
      template: EVIDENCE_INDEX,
      filename: 'index.html',
      inject: false, // Disable automatic injection
      scriptLoading: 'blocking',
      minify: {
        removeComments: true,
        collapseWhitespace: true,
        removeRedundantAttributes: true,
        useShortDoctype: true,
        removeEmptyAttributes: true,
        removeStyleLinkTypeAttributes: true,
        keepClosingSlash: true,
        minifyJS: true,
        minifyCSS: true,
        minifyURLs: true,
      },
    }),

    new CopyPlugin({
      patterns: [
        {
          from: `${EVIDENCE_BUILD_DIR}/app.js`,
          to: 'app.js',
          noErrorOnMissing: true,
        },
        {
          from: `${EVIDENCE_BUILD_DIR}/**/*`,
          to: '[name][ext]',
          noErrorOnMissing: true,
          filter: (resourcePath) => {
            // Don't copy HTML files (we're processing them), CSS files (webpack handles them), or JS files (handled above)
            return !resourcePath.endsWith('.html') && !resourcePath.endsWith('.css') && !resourcePath.endsWith('.js');
          },
        },
      ],
    }),
    // Clean up temp files after build
    {
      apply: (compiler) => {
        compiler.hooks.done.tap('CleanupTempFiles', () => {
          const tempEntry = path.resolve(__dirname, 'temp-entry.js');
          const tempBundle = path.resolve(__dirname, 'dist', 'temp-bundle.js');
          if (fs.existsSync(tempEntry)) {
            fs.unlinkSync(tempEntry);
          }
          if (fs.existsSync(tempBundle)) {
            fs.unlinkSync(tempBundle);
          }
        });
      },
    },
  ],


};

// Evidence Dashboard JavaScript
document.addEventListener('DOMContentLoaded', function() {
    console.log('Evidence Dashboard loaded');
    
    // Smooth scrolling for navigation links
    const navLinks = document.querySelectorAll('.header nav a');
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);
            
            if (targetElement) {
                targetElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
    
    // Add some interactivity to cards
    const cards = document.querySelectorAll('.card');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
            this.style.transition = 'transform 0.3s ease';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
    
    // Simulate real-time data updates (for demo purposes)
    function updateMetrics() {
        const metrics = document.querySelectorAll('.metric');
        metrics.forEach(metric => {
            const currentValue = metric.textContent;
            // Add a subtle animation to indicate data refresh
            metric.style.opacity = '0.7';
            setTimeout(() => {
                metric.style.opacity = '1';
            }, 200);
        });
    }
    
    // Update metrics every 30 seconds (for demo)
    setInterval(updateMetrics, 30000);
    
    // Add loading state management for Domo DDX
    window.domoReady = function() {
        console.log('Domo DDX environment ready');
        document.body.classList.add('domo-ready');
    };
    
    // Check if running in Domo environment
    if (window.domo) {
        console.log('Running in Domo DDX environment');
        window.domoReady();
    } else {
        console.log('Running in standalone mode');
        // Simulate Domo environment for testing
        setTimeout(() => {
            window.domoReady();
        }, 1000);
    }
    
    // Error handling for production
    window.addEventListener('error', function(e) {
        console.error('Dashboard error:', e.error);
        // In a real app, you might want to report this to an error tracking service
    });
    
    // Performance monitoring
    if ('performance' in window) {
        window.addEventListener('load', function() {
            setTimeout(() => {
                const perfData = performance.getEntriesByType('navigation')[0];
                console.log('Page load time:', perfData.loadEventEnd - perfData.loadEventStart, 'ms');
            }, 0);
        });
    }
});

// Export for potential use in Domo DDX
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        init: function() {
            console.log('Evidence Dashboard module initialized');
        }
    };
}

/* Evidence Dashboard Styles - Mimicking Evidence Framework */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    line-height: 1.6;
    color: #1f2937;
    background-color: #ffffff;
    font-size: 14px;
}

/* Evidence Container */
.evidence-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Evidence Header */
.evidence-header {
    background: #ffffff;
    border-bottom: 1px solid #e5e7eb;
    padding: 1rem 2rem;
    position: sticky;
    top: 0;
    z-index: 100;
}

.evidence-nav {
    max-width: 1200px;
    margin: 0 auto;
}

.evidence-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.25rem;
}

.evidence-breadcrumb {
    font-size: 0.875rem;
    color: #6b7280;
}

.evidence-breadcrumb span {
    color: #374151;
}

/* Evidence Main Content */
.evidence-main {
    flex: 1;
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
    width: 100%;
}

.evidence-section {
    margin-bottom: 3rem;
}

.evidence-section h2 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 1.5rem;
    border-bottom: 2px solid #e5e7eb;
    padding-bottom: 0.5rem;
}

/* Evidence Big Values */
.evidence-big-values {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.evidence-big-value {
    background: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 1.5rem;
    text-align: center;
    transition: box-shadow 0.2s ease;
}

.evidence-big-value:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.big-value-title {
    font-size: 0.875rem;
    color: #6b7280;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-weight: 500;
}

.big-value-number {
    font-size: 2.25rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 0.5rem;
    line-height: 1;
}

.big-value-change {
    font-size: 0.875rem;
    font-weight: 500;
}

.big-value-change.positive {
    color: #059669;
}

.big-value-change.negative {
    color: #dc2626;
}

.chart-container {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 3rem;
    text-align: center;
    border: 2px dashed #dee2e6;
}

.chart-placeholder {
    color: #6c757d;
    font-style: italic;
}

.table-container {
    overflow-x: auto;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
}

.data-table th,
.data-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #dee2e6;
}

.data-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #495057;
}

.data-table tbody tr:hover {
    background-color: #f8f9fa;
}

.footer {
    background: #2c3e50;
    color: white;
    text-align: center;
    padding: 2rem;
    margin-top: 3rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header {
        padding: 1rem;
    }
    
    .header nav ul {
        flex-direction: column;
        gap: 1rem;
    }
    
    .main-content {
        padding: 0 1rem;
    }
    
    .section {
        padding: 1.5rem;
    }
    
    .card-grid {
        grid-template-columns: 1fr;
    }
    
    .metric {
        font-size: 2rem;
    }
}

/* Domo DDX specific optimizations */
@media print {
    .header nav,
    .footer {
        display: none;
    }
    
    .section {
        box-shadow: none;
        border: 1px solid #dee2e6;
    }
}

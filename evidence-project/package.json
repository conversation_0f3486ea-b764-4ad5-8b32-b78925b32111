{"name": "my-evidence-project", "version": "0.0.1", "scripts": {"build": "evidence build", "build:strict": "evidence build:strict", "dev": "evidence dev --open /", "test": "evidence build", "sources": "evidence sources", "sources:strict": "evidence sources --strict", "preview": "evidence preview"}, "engines": {"npm": ">=7.0.0", "node": ">=18.0.0"}, "type": "module", "dependencies": {"@evidence-dev/bigquery": "^2.0.10", "@evidence-dev/core-components": "^5.2.2", "@evidence-dev/csv": "^1.0.14", "@evidence-dev/databricks": "^1.0.8", "@evidence-dev/evidence": "^40.1.2", "@evidence-dev/mssql": "^1.1.2", "@evidence-dev/mysql": "^1.1.4", "@evidence-dev/postgres": "^1.0.7", "@evidence-dev/snowflake": "^1.2.2", "@evidence-dev/source-javascript": "^0.0.3", "@evidence-dev/sqlite": "^2.0.7", "@evidence-dev/trino": "^1.0.9"}, "overrides": {"jsonwebtoken": "9.0.0", "trim@<0.0.3": ">0.0.3", "sqlite3": "5.1.5", "axios": "^1.7.4"}}